name: unstack
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.6.1

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  flutter_animate: ^4.5.2
  shared_preferences: ^2.5.3
  smooth_page_indicator: ^1.2.1
  swipe_deck: ^1.1.0
  confetti: ^0.8.0
  logger: ^2.6.0
  flutter_slidable: ^4.0.0
  flutter_markdown: ^0.7.4+3
  device_preview: ^1.3.1
  fluttertoast: ^8.2.12
  provider: ^6.1.5
  sqflite: ^2.4.2
  path: ^1.9.1
  uuid: ^4.5.1
  just_audio: ^0.10.4
  chiclet: ^1.2.0
  intl: ^0.20.2
  stack_trace: ^1.12.1
  url_launcher: ^6.0.9
  lottie: ^3.3.1
  table_calendar: ^3.2.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/logo/
    - assets/animation/
    - assets/files/
    - assets/sounds/
    - assets/lottie/

  fonts:
    - family: Montserrat
      fonts:
        - asset: assets/fonts/Montserrat/Montserrat-Thin.ttf
          weight: 100
        - asset: assets/fonts/Montserrat/Montserrat-ExtraLight.ttf
          weight: 200
        - asset: assets/fonts/Montserrat/Montserrat-Light.ttf
          weight: 300
        - asset: assets/fonts/Montserrat/Montserrat-Regular.ttf
          weight: 400
        - asset: assets/fonts/Montserrat/Montserrat-Medium.ttf
          weight: 500
        - asset: assets/fonts/Montserrat/Montserrat-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Montserrat/Montserrat-Bold.ttf
          weight: 700
        - asset: assets/fonts/Montserrat/Montserrat-ExtraBold.ttf
          weight: 800
        - asset: assets/fonts/Montserrat/Montserrat-Black.ttf
          weight: 900

    - family: Poppins
      fonts:
        - asset: assets/fonts/Poppins/Poppins-Thin.ttf
          weight: 100
        - asset: assets/fonts/Poppins/Poppins-ExtraLight.ttf
          weight: 200
        - asset: assets/fonts/Poppins/Poppins-Light.ttf
          weight: 300
        - asset: assets/fonts/Poppins/Poppins-Regular.ttf
          weight: 400
        - asset: assets/fonts/Poppins/Poppins-Medium.ttf
          weight: 500
        - asset: assets/fonts/Poppins/Poppins-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Poppins/Poppins-Bold.ttf
          weight: 700
        - asset: assets/fonts/Poppins/Poppins-ExtraBold.ttf
          weight: 800
        - asset: assets/fonts/Poppins/Poppins-Black.ttf
          weight: 900

  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
