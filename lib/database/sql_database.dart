import 'package:sqflite/sqflite.dart';
import 'package:unstack/models/tasks/task.model.dart';

class DatabaseService {
  static final DatabaseService instance = DatabaseService._instance();

  DatabaseService._instance();

  static Database? _db;

  String table = "tasks_table";
  String columnId = "id";
  String columnTitle = "title";
  String columnDescription = "description";
  String columnPriority = "priority";
  String columnPriorityIndex = "priorityIndex";
  String columnCreatedAt = "createdAt";
  String columnIsCompleted = "isCompleted";

  // Streak tracking
  String streakTable = "streak_table";
  String streakColumnId = "id";
  String streakColumnDate = "date";
  String streakColumnTotalTasks = "totalTasks";
  String streakColumnCompletedTasks = "completedTasks";
  String streakColumnAllTasksCompleted = "allTasksCompleted";
  String streakColumnLongestStreak = "longestStreak";
  String streakColumnCurrentStreak = "currentStreak";

  Future<Database?> get db async {
    if (_db != null) return _db;
    _db = await _initDB('tasks.db');
    return _db;
  }

  Future<Database> _initDB(String filePath) async {
    final dbPath = await getDatabasesPath();
    final path = '$dbPath/$filePath';

    final database = await openDatabase(
      path,
      version: 1,
      onCreate: _createDB,
    );

    return database;
  }

  void _createDB(Database db, int version) async {
    await db.execute('''
      CREATE TABLE $table (
        $columnId TEXT PRIMARY KEY,
        $columnTitle TEXT NOT NULL,
        $columnDescription TEXT,
        $columnPriority TEXT NOT NULL,
        $columnCreatedAt TEXT NOT NULL,
        $columnIsCompleted INTEGER NOT NULL,
        $columnPriorityIndex INTEGER NOT NULL
      )
    ''');

    await db.execute('''
      CREATE TABLE $streakTable (
        $streakColumnId INTEGER PRIMARY KEY AUTOINCREMENT,
        $streakColumnDate TEXT NOT NULL,
        $streakColumnTotalTasks INTEGER NOT NULL,
        $streakColumnCompletedTasks INTEGER NOT NULL,
        $streakColumnAllTasksCompleted INTEGER NOT NULL,
        $streakColumnLongestStreak INTEGER NOT NULL,
        $streakColumnCurrentStreak INTEGER NOT NULL
      )
    ''');
    await initialTasks(db);
  }

  Future<void> initialTasks(Database database) async {
    // Only insert initial tasks if the database is empty
    final existingResults = await database.query(table);
    if (existingResults.isEmpty) {
      final tasks = TaskData.getSampleTasks();
      for (var task in tasks) {
        await database.insert(
          table,
          task.toJson(),
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
      }
    }
  }

  Future<void> closeDB() async {
    final db = await instance.db;
    db?.close();
  }

  Future<void> deleteDB() async {
    final dbPath = await getDatabasesPath();
    final path = '$dbPath/tasks.db';
    await deleteDatabase(path);
  }

  Future<List<Map<String, dynamic>>> getResults() async {
    final Database? db = await this.db;
    final List<Map<String, dynamic>> result = await db!.query(table);
    return result;
  }

  Future<List<Task>> getTodayTasks() async {
    final Database? db = await this.db;
    final today = DateTime.now();
    final results = await db!.query(
      table,
      where: "strftime('%Y-%m-%d', $columnCreatedAt) = ?",
      whereArgs: [today.toIso8601String().split('T')[0]],
    );
    final tasks = results.map((result) => Task.fromJson(result)).toList();
    return tasks;
  }

  Future<List<Task>> getTasks() async {
    final Database? db = await this.db;
    final results = await db!.query(
      table,
      orderBy: '$columnPriorityIndex ASC',
      where: '$columnIsCompleted = 0',
    );
    final tasks = results.map((result) => Task.fromJson(result)).toList();
    return tasks;
  }

  Future<void> insertTask(Task task) async {
    final db = await instance.db;
    await db?.insert(
      table,
      task.toJson(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<void> updateTask(Task task) async {
    final db = await instance.db;
    await db?.update(
      table,
      task.toJson(),
      where: '$columnId = ?',
      whereArgs: [task.id],
    );
  }

  Future<void> markTaskAsCompleted(String taskID) async {
    final db = await instance.db;
    await db?.update(
      table,
      {columnIsCompleted: 1},
      where: '$columnId = ?',
      whereArgs: [taskID],
    );
  }

  Future<void> markTaskAsInCompleted(String taskID) async {
    final db = await instance.db;
    await db?.update(
      table,
      {columnIsCompleted: 0},
      where: '$columnId = ?',
      whereArgs: [taskID],
    );
  }

  Future<void> deleteTask(String taskId) async {
    final db = await instance.db;
    await db?.delete(table, where: '$columnId = ?', whereArgs: [taskId]);
  }

  Future<void> deleteAllTasks() async {
    final db = await instance.db;
    await db?.delete(table);
  }

  Future<void> deleteStreakData() async {
    final db = await instance.db;
    await db?.delete(streakTable);
  }

  /// Insert or update streak data for a specific date
  Future<void> insertOrUpdateStreakData(Map<String, dynamic> streakData) async {
    final db = await instance.db;
    await db?.insert(
      streakTable,
      streakData,
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  /// Get all streak completion history
  Future<List<Map<String, dynamic>>> getStreakHistory() async {
    final db = await instance.db;
    final result =
        await db?.query(streakTable, orderBy: '$streakColumnDate DESC');
    return result ?? [];
  }

  /// Get streak data for a specific date
  Future<Map<String, dynamic>?> getStreakDataForDate(String date) async {
    final db = await instance.db;
    final result = await db?.query(
      streakTable,
      where: '$streakColumnDate = ?',
      whereArgs: [date],
      limit: 1,
    );
    return result?.isNotEmpty == true ? result!.first : null;
  }

  /// Get tasks for a specific date
  Future<List<Task>> getTasksForDate(DateTime date) async {
    final db = await instance.db;
    final dateString = date.toIso8601String().split('T')[0];

    final result = await db?.query(
      table,
      where: "strftime('%Y-%m-%d', $columnCreatedAt) = ?",
      whereArgs: [dateString],
    );

    if (result == null || result.isEmpty) return [];

    return result.map((taskMap) => Task.fromJson(taskMap)).toList();
  }

  /// Delete streak data for a specific date
  Future<void> deleteStreakDataForDate(String date) async {
    final db = await instance.db;
    await db?.delete(
      streakTable,
      where: '$streakColumnDate = ?',
      whereArgs: [date],
    );
  }

  /// Get current streak count from database
  Future<int> getCurrentStreakFromDB() async {
    final db = await instance.db;
    final result = await db?.query(
      streakTable,
      columns: [streakColumnCurrentStreak],
      orderBy: '$streakColumnDate DESC',
      limit: 1,
    );

    if (result == null || result.isEmpty) return 0;
    return result.first[streakColumnCurrentStreak] as int? ?? 0;
  }

  /// Get longest streak count from database
  Future<int> getLongestStreakFromDB() async {
    final db = await instance.db;
    final result = await db?.query(
      streakTable,
      columns: ['MAX($streakColumnLongestStreak) as maxStreak'],
    );

    if (result == null || result.isEmpty) return 0;
    return result.first['maxStreak'] as int? ?? 0;
  }

  /// Get total completed days count
  Future<int> getTotalCompletedDaysFromDB() async {
    final db = await instance.db;
    final result = await db?.query(
      streakTable,
      where: '$streakColumnAllTasksCompleted = ?',
      whereArgs: [1],
    );

    return result?.length ?? 0;
  }

  /// Update streak counters in the database
  Future<void> updateStreakCounters(
      int currentStreak, int longestStreak) async {
    final db = await instance.db;
    final today = DateTime.now().toIso8601String().split('T')[0];

    // Update the most recent record with new streak values
    await db?.update(
      streakTable,
      {
        streakColumnCurrentStreak: currentStreak,
        streakColumnLongestStreak: longestStreak,
      },
      where: '$streakColumnDate = ?',
      whereArgs: [today],
    );
  }
}
