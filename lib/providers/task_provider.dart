import 'package:flutter/foundation.dart';
import 'package:unstack/logic/tasks/task_impl.dart';
import 'package:unstack/logic/streak/streak_impl.dart';
import 'package:unstack/models/tasks/task.model.dart';
import 'package:unstack/utils/app_logger.dart';

enum TaskState {
  initial,
  loading,
  loaded,
  error,
}

class TaskProvider extends ChangeNotifier {
  final TaskManager _taskManager = TaskManager();
  final StreakManager _streakManager = StreakManager();

  TaskState _taskState = TaskState.initial;
  List<Task> _tasks = [];
  String? _errorMessage;

  // Getters
  TaskState get taskState => _taskState;
  List<Task> get tasks => _tasks;
  List<Task> get remainingTasks =>
      _tasks.where((task) => !task.isCompleted).toList();
  List<Task> get completedTasks =>
      _tasks.where((task) => task.isCompleted).toList();
  String? get errorMessage => _errorMessage;
  bool get isLoading => _taskState == TaskState.loading;
  bool get hasError => _taskState == TaskState.error;

  // Statistics
  int get totalTasks => _tasks.length;
  int get completedTasksCount => completedTasks.length;
  int get remainingTasksCount => remainingTasks.length;
  double get completionPercentage =>
      totalTasks > 0 ? (completedTasksCount / totalTasks) * 100 : 0.0;

  TaskProvider() {
    loadTasks();
  }

  Task? getTaskById(String taskId) {
    return _tasks.firstWhere(
      (task) => task.id == taskId,
    );
  }

  /// Load all tasks from database
  Future<void> loadTasks() async {
    _setTaskState(TaskState.loading);

    try {
      final tasks = await _taskManager.getTasks();
      _tasks = tasks;
      _setTaskState(TaskState.loaded);
      AppLogger.info('Loaded ${tasks.length} tasks');
    } catch (e) {
      AppLogger.error('Error loading tasks: $e');
      _setError('Failed to load tasks: ${e.toString()}');
    }
  }

  /// Add a new task
  Future<bool> addTask(Task task) async {
    try {
      await _taskManager.addTask(task);
      _tasks.add(task);
      notifyListeners();

      // Update streak for the task's creation date
      await _updateStreakForTaskDate(task.createdAt);

      AppLogger.info('Added task: ${task.title}');
      return true;
    } catch (e) {
      AppLogger.error('Error adding task: $e');
      _setError('Failed to add task: ${e.toString()}');
      return false;
    }
  }

  /// Update an existing task
  Future<bool> updateTask(Task updatedTask) async {
    try {
      await _taskManager.updateTask(updatedTask);

      final index = _tasks.indexWhere((task) => task.id == updatedTask.id);
      if (index != -1) {
        final oldTask = _tasks[index];
        _tasks[index] = updatedTask;
        notifyListeners();

        // Update streak if completion status changed
        if (oldTask.isCompleted != updatedTask.isCompleted) {
          await _updateStreakForTaskDate(updatedTask.createdAt);
        }

        AppLogger.info('Updated task: ${updatedTask.title}');
        return true;
      } else {
        AppLogger.warning('Task not found for update: ${updatedTask.id}');
        return false;
      }
    } catch (e) {
      AppLogger.error('Error updating task: $e');
      _setError('Failed to update task: ${e.toString()}');
      return false;
    }
  }

  /// Delete a task
  Future<bool> deleteTask(String taskId) async {
    try {
      // Find the task before deleting to get its creation date
      final taskToDelete = _tasks.firstWhere((task) => task.id == taskId);

      await _taskManager.deleteTask(taskId);
      _tasks.removeWhere((task) => task.id == taskId);
      notifyListeners();

      // Update streak for the task's creation date
      await _updateStreakForTaskDate(taskToDelete.createdAt);

      AppLogger.info('Deleted task: $taskId');
      return true;
    } catch (e) {
      AppLogger.error('Error deleting task: $e');
      _setError('Failed to delete task: ${e.toString()}');
      return false;
    }
  }

  /// Mark task as completed
  Future<bool> markTaskAsCompleted(Task task) async {
    final completedTask = task.copyWith(isCompleted: true);
    return await updateTask(completedTask);
  }

  /// Mark task as incomplete
  Future<bool> markTaskAsIncomplete(Task task) async {
    final incompleteTask = task.copyWith(isCompleted: false);
    return await updateTask(incompleteTask);
  }

  /// Toggle task completion status
  Future<bool> toggleTaskCompletion(Task task) async {
    if (task.isCompleted) {
      return await markTaskAsIncomplete(task);
    } else {
      return await markTaskAsCompleted(task);
    }
  }

  /// Delete all tasks
  Future<bool> deleteAllTasks() async {
    try {
      await _taskManager.deleteAllTasks();
      _tasks.clear();
      notifyListeners();
      AppLogger.info('Deleted all tasks');
      return true;
    } catch (e) {
      AppLogger.error('Error deleting all tasks: $e');
      _setError('Failed to delete all tasks: ${e.toString()}');
      return false;
    }
  }

  /// Get tasks sorted by priority
  List<Task> getTasksSortedByPriority({bool ascending = false}) {
    final sortedTasks = List<Task>.from(_tasks);
    sortedTasks.sort((a, b) {
      final comparison = a.priorityIndex.compareTo(b.priorityIndex);
      return ascending ? comparison : -comparison;
    });
    return sortedTasks;
  }

  /// Get tasks sorted by creation date
  List<Task> getTasksSortedByDate({bool ascending = false}) {
    final sortedTasks = List<Task>.from(_tasks);
    sortedTasks.sort((a, b) {
      final comparison = a.createdAt.compareTo(b.createdAt);
      return ascending ? comparison : -comparison;
    });
    return sortedTasks;
  }

  /// Get tasks sorted alphabetically
  List<Task> getTasksSortedAlphabetically({bool ascending = true}) {
    final sortedTasks = List<Task>.from(_tasks);
    sortedTasks.sort((a, b) {
      final comparison = a.title.toLowerCase().compareTo(b.title.toLowerCase());
      return ascending ? comparison : -comparison;
    });
    return sortedTasks;
  }

  /// Get tasks by priority level
  List<Task> getTasksByPriority(TaskPriority priority) {
    return _tasks.where((task) => task.priority == priority).toList();
  }

  /// Get today's tasks (created today)
  List<Task> getTodaysTasks() {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    return _tasks.where((task) {
      final taskDate = DateTime(
        task.createdAt.year,
        task.createdAt.month,
        task.createdAt.day,
      );
      return taskDate.isAtSameMomentAs(today);
    }).toList();
  }

  /// Clear error state
  void clearError() {
    _errorMessage = null;
    if (_taskState == TaskState.error) {
      _setTaskState(TaskState.loaded);
    }
  }

  /// Refresh tasks from database
  Future<void> refreshTasks() async {
    await loadTasks();
  }

  // Helper methods
  void _setTaskState(TaskState state) {
    _taskState = state;
    notifyListeners();
  }

  void _setError(String message) {
    _errorMessage = message;
    _taskState = TaskState.error;
    notifyListeners();
  }

  /// Update streak data for a specific date based on tasks for that date
  Future<void> _updateStreakForTaskDate(DateTime date) async {
    try {
      // Get all tasks for the specific date
      final tasksForDate = _tasks.where((task) {
        final taskDate = DateTime(
          task.createdAt.year,
          task.createdAt.month,
          task.createdAt.day,
        );
        final targetDate = DateTime(date.year, date.month, date.day);
        return taskDate.isAtSameMomentAs(targetDate);
      }).toList();

      // Update streak data for this date
      await _streakManager.updateDayCompletion(date, tasksForDate);

      AppLogger.info(
          'Updated streak for date: ${date.toIso8601String().split('T')[0]} '
          'with ${tasksForDate.length} tasks');
    } catch (e) {
      AppLogger.error('Error updating streak for date: $e');
      // Don't rethrow to avoid breaking task operations
    }
  }
}
